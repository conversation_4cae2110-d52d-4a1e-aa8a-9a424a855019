/*
 * @Description: 学校门户接口调用
 * @Date: 2025-06-24 09:46:09
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-06-24 10:22:32
 */
import { Inject, Provide } from '@midwayjs/core';
import { APIManager } from './index.service';
import { ICourse } from './interface';

@Provide()
export class PlatformService {
  @Inject()
  apiManager: APIManager;

  /**
   * 获取学校学科列表
   * @param schoolCode 学校编号
   * @returns 学科列表
   */
  async getCourseList(schoolCode: string) {
    const result = await this.apiManager.send({
      apiCode: 'getSubjectApi',
      params: schoolCode,
    });
    return result.list as ICourse[];
  }
}
